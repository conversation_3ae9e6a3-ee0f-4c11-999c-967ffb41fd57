<?php
namespace app\crontab\command;

ini_set ("memory_limit","-1");

use AlipayService;
use Psr\Http\Message\ResponseInterface;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use think\exception\DbException;
use think\Log;

class Task extends Command
{
    // 并发数即为每批处理的订单数
    private $concurrency = 50; // 每批处理50个订单，同时50个并发
    
    protected function configure()
    {
        // php think Task
        $this->setName('Task')
            ->setDescription('task定时任务');
    }

    protected function execute(Input $input, Output $output)
    {
        while (true) {
            // 输出开始时间
//            $output->writeln("TaskCommand:start:time:".date('Y-m-d H:i:s'));
            $startTime = time();

            // 获取5分钟内的订单
            $orderList = db('order')
                ->where('pay_status', 0)
                // ->where('pay_url', 'not null')
                ->where('channel_code', 'in', ['9007','9008'])
                ->whereTime('create_time', '-5 minutes')
                ->select();

//            for($i = 0; $i < 11; $i++) {
//                $orderList = array_merge($orderList,$orderList);
//            }
            $output->writeln(date('Y-m-d H:i:s')." - 获取5分钟内的订单:".count($orderList)."笔");

            if (count($orderList) > 0) {
                try {
                    $this->processOrdersInBatches($orderList, $output);
                } catch (Exception $e) {
                    $output->writeln('支付宝账单查询失败，异常信息：'.$e->getMessage());
                }
            }

            $output->writeln("TaskCommand:execute:time:".($endTime = time() - $startTime)."s");
            // 休眠5秒
            sleep(5);
        }
    }

    /**
     * 分批处理订单 - 每批订单数 = 并发数
     */
    protected function processOrdersInBatches($orderList, $output)
    {
        $totalOrders = count($orderList);
        $batches = array_chunk($orderList, $this->concurrency);
        $batchCount = count($batches);
        
        $output->writeln("=== 开始处理 {$totalOrders} 笔订单，分为 {$batchCount} 批，每批 {$this->concurrency} 个并发 ===");
        
        foreach ($batches as $batchIndex => $batch) {
            $batchNumber = $batchIndex + 1;
            $batchSize = count($batch);
            $batchStartTime = microtime(true);
            
            $output->writeln("处理第 {$batchNumber}/{$batchCount} 批，订单数: {$batchSize}");
            
            $this->alipayCheckBatch($batch, $output);
            
            $batchTime = round((microtime(true) - $batchStartTime) * 1000, 2);
            $output->writeln("  └── 批次完成，耗时: {$batchTime}ms");
            
            // 批次间短暂休息
            if ($batchNumber < $batchCount) {
                usleep(100000); // 0.1秒
            }
        }
        
        $output->writeln("=== 所有批次处理完成 ===");
    }

    /**
     * 处理单个批次的订单
     */
    protected function alipayCheckBatch($orderList)
    {
        $client = new Client(['verify' => false]);
        $promises = [];
        
        foreach ($orderList as $order) {
            $start_time = datetime($order['create_time']);
            $end_time = datetime(time());
            
            // 获取账号信息
            $account = db('account')->where('account_identity', $order['account_identity'])->find();
            if(empty($account)) continue;
            
            $accountConfig = json_decode($account['config'], true, 512, JSON_THROW_ON_ERROR);

            $uri = 'https://openapi.alipay.com/gateway.do';
            $requestConfigs = array(
                'start_time' => $start_time,
                'end_time' => $end_time,
            );
            $commonConfigs = array(
                'app_id' => '****************',
                'method' => 'alipay.data.bill.accountlog.query',
                'format' => 'JSON',
                'charset'=> 'utf-8',
                'sign_type'=> 'RSA2',
                'timestamp'=> $start_time,
                'version'=> '1.0',
                'biz_content'=> json_encode($requestConfigs),
            );

            if (!empty($accountConfig['app_auth_token'])) {
                $commonConfigs['app_auth_token'] = $accountConfig['app_auth_token'];
            }

            $aliPay = new AlipayService();
            $aliPay->setAppid('****************');
            $commonConfigs["sign"] = $aliPay->generateSign($commonConfigs, $commonConfigs['sign_type']);
            $x = $aliPay->buildOrderStr($commonConfigs);

            // 创建异步请求
            $promises[] = $client->getAsync("{$uri}?$x")
                ->then(function (Response $response) use ($order, $output) {
                    $result = json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR)['alipay_data_bill_accountlog_query_response'];

                    if($result['code'] != 10000) {
                        $output->writeln($order['out_trade_no'].'支付宝账单查询失败，返回信息：'.$result['msg']);
                        return;
                    }

                    $output->writeln('支付宝账单查询成功，返回信息：'.json_encode($result));
                    if (!empty($result['detail_list'])) {
                        foreach ($result['detail_list'] as $detail) {
                            if($detail['trans_amount']==$order['amount'] && $detail['direction']=="收入") {
                                $od = db('order')->where('pay_trade_no',$detail['alipay_order_no'])->find();
                                if(empty($od)){
                                    db('order')
                                        ->where('id', $order['id'])
                                        ->update(['pay_status' => 1, 'pay_trade_no' => $detail['alipay_order_no']]);
                                    
                                    $merchants = db('merchants')->where('id', $order['merchants_id'])->find();
                                    if (\app\common\library\Order::merchantsCallback($order['callback_url'], $merchants['key'], $order['out_trade_no'], $order['amount'], $order['channel_code'], 1)) {
                                        db('order')
                                            ->where('id', $order['id'])
                                            ->update(['callback_status' => 1, 'callback_time' => time()]);
                                    } else {
                                        $output->writeln('【回调失败】 订单号：'.$order['out_trade_no']);
                                    }
                                }
                            }
                        }
                    }
                })
                ->otherwise(function (RequestException $reason) use ($order, $output) {
                    $output->writeln('订单号：'.$order['out_trade_no'].'账号：'.$order['account_identity'].'发生未知异常，异常信息：'.$reason->getMessage());
                });
        }

        // 等待当前批次的所有请求完成
        \GuzzleHttp\Promise\Utils::settle($promises)->wait();
    }

    protected function alipayCheck($orderList){
        $client = new Client(['verify' => false]);

        // 为每个订单单独处理，避免并发索引混乱
        $promises = [];
        foreach ($orderList as $key => $order) {
            $start_time = datetime($order['create_time']);
            $end_time = datetime(time());
            
            // 获取账号信息
            $account = db('account')->where('account_identity', $order['account_identity'])->find();
            if(empty($account)) continue;
            
            $accountConfig = json_decode($account['config'], true, 512, JSON_THROW_ON_ERROR);

            $uri = 'https://openapi.alipay.com/gateway.do';
            $requestConfigs = array(
                'start_time' => $start_time,
                'end_time' => $end_time,
            );
            $commonConfigs = array(
                'app_id' => '****************',
                'method' => 'alipay.data.bill.accountlog.query',
                'format' => 'JSON',
                'charset'=> 'utf-8',
                'sign_type'=> 'RSA2',
                'timestamp'=> $start_time,
                'version'=> '1.0',
                'biz_content'=> json_encode($requestConfigs),
            );

            if (!empty($accountConfig['app_auth_token'])) {
                $commonConfigs['app_auth_token'] = $accountConfig['app_auth_token'];
            }

            $aliPay = new AlipayService();
            $aliPay->setAppid('****************');
        }
    }
}